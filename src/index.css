@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 255 255 255;
    --foreground: 17 24 39;
    --card: 255 255 255;
    --card-foreground: 17 24 39;
    --popover: 255 255 255;
    --popover-foreground: 17 24 39;

    /* Primary Colors - Blue Family */
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --primary-light: 147 197 253;
    --primary-dark: 37 99 235;

    /* Secondary Colors - Purple Family */
    --secondary: 139 92 246;
    --secondary-foreground: 255 255 255;
    --secondary-light: 196 181 253;
    --secondary-dark: 124 58 237;

    /* Accent Colors - Emerald Family */
    --accent: 16 185 129;
    --accent-foreground: 255 255 255;
    --accent-light: 110 231 183;
    --accent-dark: 5 150 105;

    /* Warning Colors - Amber Family */
    --warning: 245 158 11;
    --warning-foreground: 255 255 255;
    --warning-light: 252 211 77;
    --warning-dark: 217 119 6;

    /* Success Colors - Green Family */
    --success: 34 197 94;
    --success-foreground: 255 255 255;
    --success-light: 134 239 172;
    --success-dark: 22 163 74;

    /* Error Colors - Red Family */
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --destructive-light: 252 165 165;
    --destructive-dark: 220 38 38;

    /* Neutral Colors */
    --muted: 249 250 251;
    --muted-foreground: 107 114 128;
    --border: 229 231 235;
    --input: 229 231 235;
    --ring: 59 130 246;
    --radius: 0.5rem;

    /* Info Colors - Cyan Family */
    --info: 6 182 212;
    --info-foreground: 255 255 255;
    --info-light: 103 232 249;
    --info-dark: 8 145 178;
  }

  .dark {
    --background: 15 23 42;
    --foreground: 248 250 252;
    --card: 30 41 59;
    --card-foreground: 248 250 252;
    --popover: 30 41 59;
    --popover-foreground: 248 250 252;

    /* Primary Colors - Blue Family */
    --primary: 99 102 241;
    --primary-foreground: 255 255 255;
    --primary-light: 165 180 252;
    --primary-dark: 67 56 202;

    /* Secondary Colors - Purple Family */
    --secondary: 168 85 247;
    --secondary-foreground: 255 255 255;
    --secondary-light: 196 181 253;
    --secondary-dark: 147 51 234;

    /* Accent Colors - Emerald Family */
    --accent: 34 197 94;
    --accent-foreground: 255 255 255;
    --accent-light: 110 231 183;
    --accent-dark: 22 163 74;

    /* Warning Colors - Amber Family */
    --warning: 251 191 36;
    --warning-foreground: 17 24 39;
    --warning-light: 253 230 138;
    --warning-dark: 245 158 11;

    /* Success Colors - Green Family */
    --success: 34 197 94;
    --success-foreground: 255 255 255;
    --success-light: 134 239 172;
    --success-dark: 22 163 74;

    /* Error Colors - Red Family */
    --destructive: 248 113 113;
    --destructive-foreground: 255 255 255;
    --destructive-light: 252 165 165;
    --destructive-dark: 239 68 68;

    /* Neutral Colors */
    --muted: 51 65 85;
    --muted-foreground: 148 163 184;
    --border: 51 65 85;
    --input: 51 65 85;
    --ring: 99 102 241;

    /* Info Colors - Cyan Family */
    --info: 34 211 238;
    --info-foreground: 17 24 39;
    --info-light: 103 232 249;
    --info-dark: 6 182 212;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-medium;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      sans-serif;
    font-feature-settings: "cv11", "ss01";
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: rgb(var(--foreground));
    background-color: rgb(var(--background));
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(142, 142, 147, 0.3);
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(142, 142, 147, 0.5);
    background-clip: content-box;
  }

  .dark ::-webkit-scrollbar-thumb {
    background: rgba(174, 174, 178, 0.3);
    background-clip: content-box;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgba(174, 174, 178, 0.5);
    background-clip: content-box;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass-card {
    @apply bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/60 dark:border-slate-700/60 rounded-2xl shadow-lg;
  }

  /* Flat card style */
  .flat-card {
    @apply bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-2xl;
  }

  /* Button variants */
  .btn-primary {
    @apply bg-primary hover:bg-primary-dark text-primary-foreground font-medium py-2.5 px-6 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md;
    background-color: rgb(var(--primary));
    color: rgb(var(--primary-foreground));
  }

  .btn-primary:hover {
    background-color: rgb(var(--primary-dark));
  }

  .btn-secondary {
    @apply bg-secondary hover:bg-secondary-dark text-secondary-foreground font-medium py-2.5 px-6 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md;
    background-color: rgb(var(--secondary));
    color: rgb(var(--secondary-foreground));
  }

  .btn-secondary:hover {
    background-color: rgb(var(--secondary-dark));
  }

  .btn-accent {
    @apply bg-accent hover:bg-accent-dark text-accent-foreground font-medium py-2.5 px-6 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md;
    background-color: rgb(var(--accent));
    color: rgb(var(--accent-foreground));
  }

  .btn-accent:hover {
    background-color: rgb(var(--accent-dark));
  }

  .btn-warning {
    @apply bg-warning hover:bg-warning-dark text-warning-foreground font-medium py-2.5 px-6 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md;
    background-color: rgb(var(--warning));
    color: rgb(var(--warning-foreground));
  }

  .btn-warning:hover {
    background-color: rgb(var(--warning-dark));
  }

  .btn-success {
    @apply bg-success hover:bg-success-dark text-success-foreground font-medium py-2.5 px-6 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md;
    background-color: rgb(var(--success));
    color: rgb(var(--success-foreground));
  }

  .btn-success:hover {
    background-color: rgb(var(--success-dark));
  }

  .btn-ghost {
    @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 font-medium py-2.5 px-6 rounded-xl transition-all duration-200;
  }

  /* Input styles */
  .input-modern {
    @apply bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded-xl px-4 py-3 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-primary transition-all duration-200;
    --tw-ring-color: rgb(var(--primary) / 0.5);
  }

  /* Card styles */
  .card-modern {
    @apply bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-2xl shadow-sm hover:shadow-md transition-all duration-200;
  }

  /* Navigation styles */
  .nav-modern {
    @apply bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-gray-200/60 dark:border-slate-700/60;
  }

  /* Status indicators */
  .status-success {
    @apply bg-success-light/20 dark:bg-success-dark/20 text-success-dark dark:text-success-light border border-success-light dark:border-success-dark;
    background-color: rgb(var(--success-light) / 0.2);
    color: rgb(var(--success-dark));
    border-color: rgb(var(--success-light));
  }

  .status-warning {
    @apply bg-warning-light/20 dark:bg-warning-dark/20 text-warning-dark dark:text-warning-light border border-warning-light dark:border-warning-dark;
    background-color: rgb(var(--warning-light) / 0.2);
    color: rgb(var(--warning-dark));
    border-color: rgb(var(--warning-light));
  }

  .status-error {
    @apply bg-destructive-light/20 dark:bg-destructive-dark/20 text-destructive-dark dark:text-destructive-light border border-destructive-light dark:border-destructive-dark;
    background-color: rgb(var(--destructive-light) / 0.2);
    color: rgb(var(--destructive-dark));
    border-color: rgb(var(--destructive-light));
  }

  .status-info {
    @apply bg-info-light/20 dark:bg-info-dark/20 text-info-dark dark:text-info-light border border-info-light dark:border-info-dark;
    background-color: rgb(var(--info-light) / 0.2);
    color: rgb(var(--info-dark));
    border-color: rgb(var(--info-light));
  }

  /* Text gradients */
  .text-gradient {
    @apply text-gray-900 dark:text-gray-100;
    background: none;
  }

  /* Language badge colors */
  .lang-javascript {
    @apply bg-yellow-500 text-white;
  }
  .lang-typescript {
    @apply bg-blue-500 text-white;
  }
  .lang-python {
    @apply bg-green-500 text-white;
  }
  .lang-java {
    @apply bg-red-500 text-white;
  }
  .lang-go {
    @apply bg-cyan-500 text-white;
  }
  .lang-rust {
    @apply bg-orange-500 text-white;
  }
  .lang-php {
    @apply bg-purple-500 text-white;
  }
  .lang-ruby {
    @apply bg-red-600 text-white;
  }
  .lang-swift {
    @apply bg-orange-600 text-white;
  }
  .lang-kotlin {
    @apply bg-purple-600 text-white;
  }
  .lang-dart {
    @apply bg-blue-600 text-white;
  }
  .lang-scala {
    @apply bg-red-700 text-white;
  }
  .lang-clojure {
    @apply bg-green-600 text-white;
  }
  .lang-haskell {
    @apply bg-purple-700 text-white;
  }
  .lang-cpp {
    @apply bg-blue-700 text-white;
  }
  .lang-c {
    @apply bg-gray-600 text-white;
  }
  .lang-csharp {
    @apply bg-purple-800 text-white;
  }
  .lang-html {
    @apply bg-orange-500 text-white;
  }
  .lang-css {
    @apply bg-blue-500 text-white;
  }
  .lang-json {
    @apply bg-green-500 text-white;
  }
  .lang-markdown {
    @apply bg-gray-500 text-white;
  }
  .lang-sql {
    @apply bg-indigo-500 text-white;
  }
  .lang-bash {
    @apply bg-gray-700 text-white;
  }
  .lang-shell {
    @apply bg-gray-700 text-white;
  }

  /* Animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Utilities */
  .text-balance {
    text-wrap: balance;
  }

  .backdrop-blur-glass {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }
}

/* Additional utilities for modern UI */
@layer utilities {
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .shadow-apple {
    box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.1),
      0 2px 10px -4px rgba(0, 0, 0, 0.05);
  }

  .dark .shadow-apple {
    box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.4),
      0 2px 10px -4px rgba(0, 0, 0, 0.3);
  }

  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Color utilities */
  .bg-primary {
    background-color: rgb(var(--primary));
  }
  .bg-primary-light {
    background-color: rgb(var(--primary-light));
  }
  .bg-primary-dark {
    background-color: rgb(var(--primary-dark));
  }

  .bg-secondary {
    background-color: rgb(var(--secondary));
  }
  .bg-secondary-light {
    background-color: rgb(var(--secondary-light));
  }
  .bg-secondary-dark {
    background-color: rgb(var(--secondary-dark));
  }

  .bg-accent {
    background-color: rgb(var(--accent));
  }
  .bg-accent-light {
    background-color: rgb(var(--accent-light));
  }
  .bg-accent-dark {
    background-color: rgb(var(--accent-dark));
  }

  .bg-success {
    background-color: rgb(var(--success));
  }
  .bg-success-light {
    background-color: rgb(var(--success-light));
  }
  .bg-success-dark {
    background-color: rgb(var(--success-dark));
  }

  .bg-warning {
    background-color: rgb(var(--warning));
  }
  .bg-warning-light {
    background-color: rgb(var(--warning-light));
  }
  .bg-warning-dark {
    background-color: rgb(var(--warning-dark));
  }

  .bg-destructive {
    background-color: rgb(var(--destructive));
  }
  .bg-destructive-light {
    background-color: rgb(var(--destructive-light));
  }
  .bg-destructive-dark {
    background-color: rgb(var(--destructive-dark));
  }

  .bg-info {
    background-color: rgb(var(--info));
  }
  .bg-info-light {
    background-color: rgb(var(--info-light));
  }
  .bg-info-dark {
    background-color: rgb(var(--info-dark));
  }

  .text-primary {
    color: rgb(var(--primary));
  }
  .text-secondary {
    color: rgb(var(--secondary));
  }
  .text-accent {
    color: rgb(var(--accent));
  }
  .text-success {
    color: rgb(var(--success));
  }
  .text-warning {
    color: rgb(var(--warning));
  }
  .text-destructive {
    color: rgb(var(--destructive));
  }
  .text-info {
    color: rgb(var(--info));
  }
}
